import { <PERSON>ui<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EuiTitle } from '@elastic/eui';
import React, { useState } from 'react';
import { ImageCell, MetricCell } from '@/curator/components/table/cells';

interface Product {
    id: string;
    image: string;
    name: string;
    ctr: number;
    impressions: number;
    gmiClick: string;
    margin: number;
    volume: number;
    isPinned: boolean;
}

interface ProductsTabProps {
    products: any[];
    isLoading?: boolean;
}

// Dummy data for VIEW mode
const DUMMY_COLLECTION_PRODUCTS: Product[] = [
    {
        id: 'P001',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Organic Bananas',
        ctr: 3.2,
        impressions: 15000,
        gmiClick: '$2.45',
        margin: 25.3,
        volume: 1250,
        isPinned: true,
    },
    {
        id: 'P002',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Fresh Strawberries',
        ctr: 2.8,
        impressions: 12000,
        gmiClick: '$3.12',
        margin: 18.9,
        volume: 890,
        isPinned: false,
    },
];

const ProductsTab: React.FC<ProductsTabProps> = ({ products: _, isLoading }) => {
    const [collectionProducts] = useState(DUMMY_COLLECTION_PRODUCTS);
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 20,
        totalItemCount: DUMMY_COLLECTION_PRODUCTS.length,
        pageSizeOptions: [10, 20, 50],
    });

    const handleTableChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };

    const columns = [
      
        {
            field: 'id',
            name: 'Product ID',
            width: '100px',
        },
        {
            field: 'image',
            name: '',
            render: (image: string) => <ImageCell src={image} size="s" data={{}} alt="" />,
            width: '48px',
        },
        {
            field: 'name',
            name: 'Product name',
        },
        {
            field: 'ctr',
            name: 'CTR ↓',
            render: (value: number) => <MetricCell data={{}} value={value} suffix=" %" />,
        },
        {
            field: 'impressions',
            name: 'Impressions',
            render: (value: number) => <MetricCell data={{}} value={value} />,
        },
        {
            field: 'gmiClick',
            name: 'GMI/click',
            render: (value: string) => <MetricCell data={{}} value={value} />,
        },
        {
            field: 'margin',
            name: 'Margin',
            render: (value: number) => <MetricCell data={{}} value={value} suffix="%" />,
        },
        {
            field: 'volume',
            name: 'Volume',
            render: (value: number) => <MetricCell data={{}} value={value} />,
        },
        {
            field: 'isPinned',
            name: 'Pinned',
            render: (isPinned: boolean) => (
                <span>{isPinned ? '📌' : ''}</span>
            ),
        },
    ];

    return (
        <div data-test-subj="products-tab">
            <>
                <EuiTitle size="s">
                    <h3>Collection</h3>
                </EuiTitle>
                <EuiSpacer size="l" />

                <EuiBasicTable
                    items={collectionProducts}
                    columns={columns}
                    loading={isLoading}
                    pagination={pagination}
                    onChange={handleTableChange}
                
                />
            </>
        </div>
    );
};

export default ProductsTab;