import { <PERSON>ui<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ui<PERSON>it<PERSON> } from '@elastic/eui';
import React, { useState } from 'react';
import { getViewProductColumns, ViewProduct } from '@/curator/pages/themes/shared/productColumns';

interface ProductsTabProps {
    products: any[];
    isLoading?: boolean;
}

// Dummy data for VIEW mode
const DUMMY_COLLECTION_PRODUCTS: ViewProduct[] = [
    {
        id: 'P001',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Organic Bananas',
        ctr: 3.2,
        impressions: 15000,
        gmiClick: '$2.45',
        margin: 25.3,
        volume: 1250,
        pinned: true,
    },
    {
        id: 'P002',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Fresh Strawberries',
        ctr: 2.8,
        impressions: 12000,
        gmiClick: '$3.12',
        margin: 18.9,
        volume: 890,
        pinned: false,
    },
];

const ProductsTab: React.FC<ProductsTabProps> = ({ products: _, isLoading }) => {
    const [collectionProducts] = useState(DUMMY_COLLECTION_PRODUCTS);
    const [pagination, setPagination] = useState({
        pageIndex: 0,
        pageSize: 20,
        totalItemCount: DUMMY_COLLECTION_PRODUCTS.length,
        pageSizeOptions: [10, 20, 50],
    });

    const handleTableChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };

    const columns = getViewProductColumns();

    return (
        <div data-test-subj="products-tab">
            <>
                <EuiTitle size="s">
                    <h3>Collection</h3>
                </EuiTitle>
                <EuiSpacer size="l" />

                <EuiBasicTable
                    items={collectionProducts}
                    columns={columns}
                    loading={isLoading}
                    pagination={pagination}
                    onChange={handleTableChange}
                
                />
            </>
        </div>
    );
};

export default ProductsTab;