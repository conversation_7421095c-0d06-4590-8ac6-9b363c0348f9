import { useState } from 'react';
import { EditProduct } from '@/curator/pages/themes/shared/productColumns';

const mockCollectionProducts: EditProduct[] = [
    {
        id: 'P001',
        image: 'https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg',
        name: 'Organic Bananas',
        conversion: 12.5,
        margin: 25.3,
        dhf: 8.2,
        volume: 1250,
    },
    {
        id: 'P002',
        image: 'https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg',
        name: 'Fresh Strawberries',
        conversion: 8.7,
        margin: 18.9,
        dhf: 6.1,
        volume: 890,
    },
];

const mockSearchProducts: EditProduct[] = [
    {
        id: 'P003',
        image: 'https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg',
        name: 'Avocados',
        conversion: 15.2,
        margin: 32.1,
        dhf: 9.8,
        volume: 567,
    },
    {
        id: 'P004',
        image: 'https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg',
        name: 'Blueberries',
        conversion: 11.8,
        margin: 28.7,
        dhf: 7.4,
        volume: 423,
    },
];

const mockRecommendations: EditProduct[] = [
    {
        id: 'P005',
        image: 'https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg',
        name: 'Greek Yogurt',
        conversion: 9.3,
        margin: 22.5,
        dhf: 5.9,
        volume: 789,
    },
];

export const useProductsData = () => {
    const [collectionProducts, setCollectionProducts] = useState<EditProduct[]>(mockCollectionProducts);
    const [searchProducts] = useState<EditProduct[]>(mockSearchProducts);
    const [recommendations, setRecommendations] = useState<EditProduct[]>(mockRecommendations);
    
    const [searchPagination, setSearchPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
        totalItemCount: 50,
        pageSizeOptions: [10, 25, 50],
    });

    const handleProductAction = async (action: string, productId: string) => {
        if (action === 'remove') {
            setCollectionProducts(prev => prev.filter(p => p.id !== productId));
        } else if (action === 'add') {
            const sourceProduct = [...searchProducts, ...recommendations].find(p => p.id === productId);
            if (sourceProduct && !collectionProducts.find(p => p.id === productId)) {
                setCollectionProducts(prev => [...prev, sourceProduct]);
            }
        } else if (action === 'pin') {
            setCollectionProducts(prev => 
                prev.map(product => 
                    product.id === productId 
                        ? { ...product, pinned: !product.pinned }
                        : product
                )
            );
        }
    };

    const handleSearchPageChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setSearchPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };

    return {
        collectionProducts,
        searchProducts,
        recommendations,
        searchPagination,
        handleProductAction,
        handleSearchPageChange,
        setCollectionProducts,
        setRecommendations,
    };
};
