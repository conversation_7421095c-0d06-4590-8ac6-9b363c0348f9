/**
 * Theme Products Tab - Manages product selection for themes
 *
 * This component provides three main sections:
 * - Collection: Drag & drop reorderable list of selected products with pin/remove actions
 * - Search: Paginated product search with add-to-collection functionality
 * - Recommendations: AI-powered product suggestions based on current collection
 */

import {
    EuiButton,
    EuiFlexGroup,
    EuiFlexItem,
    EuiSpacer,
    euiDragDropReorder,
} from '@elastic/eui';
import React from 'react';
import ProductCollection from './components/ProductCollection';
import ProductSearch from './components/ProductSearch';
import ProductRecommendation from './components/ProductRecommendation';
import { getEditProductColumns } from '@/curator/pages/themes/shared/productColumns';
import { useProductsData } from './hooks';

interface ThemeProductsTabProps {
    onNext: () => void;
}

const ThemeProductsTab: React.FC<ThemeProductsTabProps> = ({ onNext }) => {
    const {
        collectionProducts,
        searchProducts,
        recommendations,
        searchPagination,
        handleProductAction,
        handleSearchPageChange,
        setCollectionProducts,
    } = useProductsData();

    const onDragEnd = ({ source, destination }: any) => {
        if (source && destination) {
            const items = euiDragDropReorder(collectionProducts, source.index, destination.index);
            setCollectionProducts(items);
        }
    };

    return (
        <>
            <EuiFlexGroup direction="column">
                <EuiFlexItem>
                    <ProductCollection 
                        collectionProducts={collectionProducts}
                        onDragEnd={onDragEnd}
                        handleProductAction={handleProductAction}
                    />
                </EuiFlexItem>

                <EuiFlexItem>
                    <ProductSearch
                        searchProducts={searchProducts}
                        searchPagination={searchPagination}
                        columns={getEditProductColumns(handleProductAction, collectionProducts)}
                        handleSearchPageChange={handleSearchPageChange}
                    />
                </EuiFlexItem>

                <EuiFlexItem>
                    <ProductRecommendation
                        recommendations={recommendations}
                        columns={getEditProductColumns(handleProductAction, collectionProducts)}
                    />
                </EuiFlexItem>
            </EuiFlexGroup>

            <EuiSpacer size="l" />

            <EuiButton iconType="arrowRight" iconSide="right" onClick={onNext}>
                Continue to Performance
            </EuiButton>
        </>
    );
};

export default ThemeProductsTab;
