import {
    EuiBasicTable,
    EuiBasicTableColumn,
    EuiFieldSearch,
    EuiFlexGroup,
    EuiFlexItem,
    EuiSelect,
    EuiSpacer,
    EuiTitle,
} from '@elastic/eui';
import React from 'react';
import { EditProduct } from '@/curator/pages/themes/shared/productColumns';

interface ProductSearchProps {
    searchProducts: EditProduct[];
    searchPagination: any;
    columns: EuiBasicTableColumn<EditProduct>[];
    handleSearchPageChange: (params: any) => void;
}

const ProductSearch: React.FC<ProductSearchProps> = ({
    searchProducts,
    searchPagination,
    columns,
    handleSearchPageChange,
}) => {
    return (
        <>
            <EuiTitle size="s">
                <h3>Find products</h3>
            </EuiTitle>
            <EuiSpacer size="m" />

            <EuiFlexGroup gutterSize="m">
                <EuiFlexItem>
                    <EuiSelect
                        options={[
                            { value: 'all', text: 'Categories' },
                            { value: 'category1', text: 'Category 1' },
                            { value: 'category2', text: 'Category 2' },
                        ]}
                    />
                </EuiFlexItem>
                <EuiFlexItem grow={2}>
                    <EuiFieldSearch placeholder="Search" />
                </EuiFlexItem>
            </EuiFlexGroup>

            <EuiSpacer size="m" />

            <EuiBasicTable
                items={searchProducts}
                columns={columns}
                pagination={searchPagination}
                onChange={handleSearchPageChange}
            />
        </>
    );
};

export default ProductSearch;
