import { EuiBasicTableColumn } from '@elastic/eui';
import type { TableAction } from '@/curator/components/table/cells';
import { ActionCell, ImageCell, MetricCell, TextValueCell } from '@/curator/components/table/cells';

export interface Product {
    id: string;
    image: string;
    name: string;
    conversion: number;
    margin: number;
    dhf: number;
    volume: number;
    isPinned?: boolean;
}

export type ProductActionHandler = (action: string, productId: string) => void;

/**
 * Base product columns that are common across all product tables
 */
const getBaseProductColumns = (): EuiBasicTableColumn<Product>[] => [
    {
        field: 'id',
        name: 'Product ID',
        width: '100px',
        render: (id: string, product: Product) => (
            <TextValueCell data={product} value={id} />
        ),
    },
    {
        field: 'image',
        name: '',
        width: '60px',
        render: (image: string, product: Product) => (
            <ImageCell src={image} size="s" data={product} alt="" />
        ),
    },
    {
        field: 'name',
        name: 'Product name',
        render: (name: string, product: Product) => (
            <TextValueCell data={product} value={name} />
        ),
    },
    {
        field: 'conversion',
        name: 'Conversion',
        render: (conversion: number, product: Product) => (
            <MetricCell data={product} value={conversion} suffix=" %" />
        ),
    },
    {
        field: 'margin',
        name: 'Margin',
        render: (margin: number, product: Product) => (
            <MetricCell data={product} value={margin} suffix="%" />
        ),
    },
    {
        field: 'dhf',
        name: 'DHF',
        render: (dhf: number, product: Product) => (
            <MetricCell data={product} value={dhf} />
        ),
    },
    {
        field: 'volume',
        name: 'Volume',
        render: (volume: number, product: Product) => (
            <MetricCell data={product} value={volume} />
        ),
    },
];

/**
 * Get columns for product search tables (with add action)
 */
export const getSearchProductColumns = (
    handleProductAction: ProductActionHandler,
    collectionProducts: Product[]
): EuiBasicTableColumn<Product>[] => {
    const searchActions: TableAction<Product>[] = [
        {
            id: 'add',
            label: 'Add',
            iconType: 'plus',
            onClick: (item: Product) => handleProductAction('add', item.id),
            isCompleted: (item: Product) => collectionProducts.some(p => p.id === item.id),
            completedIcon: 'check',
        },
    ];

    return [
        ...getBaseProductColumns(),
        {
            field: 'actions',
            name: 'Actions',
            width: '80px',
            render: (_: any, product: Product) => (
                <ActionCell item={product} actions={searchActions} />
            ),
        },
    ];
};

/**
 * Get columns for product recommendation tables (with add action)
 */
export const getRecommendationProductColumns = (
    handleProductAction: ProductActionHandler,
    collectionProducts: Product[]
): EuiBasicTableColumn<Product>[] => {
    // Recommendations use the same actions as search
    return getSearchProductColumns(handleProductAction, collectionProducts);
};

/**
 * Get actions for collection products (pin and remove)
 */
export const getCollectionProductActions = (
    handleProductAction: ProductActionHandler
): TableAction<Product>[] => [
    {
        id: 'pin',
        label: 'Pin',
        iconType: 'pin',
        onClick: (item: Product) => handleProductAction('pin', item.id),
        isToggled: (item: Product) => item.isPinned || false,
        toggledIcon: 'pinFilled',
    },
    {
        id: 'remove',
        label: 'Remove',
        iconType: 'cross',
        onClick: (item: Product) => handleProductAction('remove', item.id),
    },
];

/**
 * Get columns for read-only product tables (no actions)
 */
export const getReadOnlyProductColumns = (): EuiBasicTableColumn<Product>[] => {
    return getBaseProductColumns();
};

/**
 * Get columns for editable product tables (with custom actions)
 */
export const getEditableProductColumns = (
    actions: TableAction<Product>[]
): EuiBasicTableColumn<Product>[] => {
    return [
        ...getBaseProductColumns(),
        {
            field: 'actions',
            name: 'Actions',
            width: '80px',
            render: (_: any, product: Product) => (
                <ActionCell item={product} actions={actions} />
            ),
        },
    ];
};
