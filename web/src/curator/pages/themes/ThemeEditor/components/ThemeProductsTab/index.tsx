import {
    EuiButton,
    EuiFlexGroup,
    EuiFlexItem,
    EuiSpacer,
    euiDragDropReorder,
} from '@elastic/eui';
import React, { useState } from 'react';
import ProductCollection from './ProductCollection';
import ProductSearch from './ProductSearch';
import ProductRecommendation from './ProductRecommendation';
import {
    EditProduct,
    getEditProductColumnsWithAdd
} from '@/curator/pages/themes/shared/productColumns';



interface ThemeProductsTabProps {
    onNext: () => void;
}

const ThemeProductsTab: React.FC<ThemeProductsTabProps> = ({ onNext }) => {
    // Mock data - will be replaced with API calls later
    const [collectionProducts, setCollectionProducts] = useState<EditProduct[]>([
        {
            id: 'P001',
            image: '/api/placeholder/40/40',
            name: 'Organic Bananas',
            conversion: 12.5,
            margin: 25.3,
            dhf: 8.2,
            volume: 1250,
        },
        {
            id: 'P002',
            image: '/api/placeholder/40/40',
            name: 'Fresh Strawberries',
            conversion: 8.7,
            margin: 18.9,
            dhf: 6.1,
            volume: 890,
        },
    ]);

    const [searchProducts] = useState<EditProduct[]>([
        {
            id: 'P003',
            image: '/api/placeholder/40/40',
            name: 'Avocados',
            conversion: 15.2,
            margin: 32.1,
            dhf: 9.8,
            volume: 567,
        },
        {
            id: 'P004',
            image: '/api/placeholder/40/40',
            name: 'Blueberries',
            conversion: 11.8,
            margin: 28.7,
            dhf: 7.4,
            volume: 423,
        },
    ]);

    const [recommendations] = useState<EditProduct[]>([
        {
            id: 'P005',
            image: '/api/placeholder/40/40',
            name: 'Greek Yogurt',
            conversion: 9.3,
            margin: 22.5,
            dhf: 5.9,
            volume: 789,
        },
    ]);

    const [searchPagination, setSearchPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
        totalItemCount: 50,
        pageSizeOptions: [10, 25, 50],
    });

    const onDragEnd = ({ source, destination }: any) => {
        if (source && destination) {
            const items = euiDragDropReorder(collectionProducts, source.index, destination.index);
            setCollectionProducts(items);
        }
    };

    const handleProductAction = (action: string, productId: string) => {
        console.log(`${action} product:`, productId);
        if (action === 'remove') {
            setCollectionProducts(prev => prev.filter(p => p.id !== productId));
        } else if (action === 'add') {
            // Add product to collection from search/recommendations
            const sourceProduct = [...searchProducts, ...recommendations].find(p => p.id === productId);
            if (sourceProduct && !collectionProducts.find(p => p.id === productId)) {
                setCollectionProducts(prev => [...prev, sourceProduct]);
            }
        } else if (action === 'pin') {
            // Toggle pin status for product in collection
            setCollectionProducts(prev => 
                prev.map(product => 
                    product.id === productId 
                        ? { ...product, isPinned: !product.isPinned }
                        : product
                )
            );
        }
    };

    const handleSearchPageChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setSearchPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };



    return (
        <>
            <EuiFlexGroup direction="column">
                {/* Collection Section */}
                <EuiFlexItem>
                    <ProductCollection 
                        collectionProducts={collectionProducts}
                        onDragEnd={onDragEnd}
                        handleProductAction={handleProductAction}
                    />
                </EuiFlexItem>

                {/* Search Section */}
                <EuiFlexItem>
                    <ProductSearch
                        searchProducts={searchProducts}
                        searchPagination={searchPagination}
                        columns={getEditProductColumnsWithAdd(handleProductAction, collectionProducts)}
                        handleSearchPageChange={handleSearchPageChange}
                    />
                </EuiFlexItem>

                {/* Recommendations Section */}
                <EuiFlexItem>
                    <ProductRecommendation
                        recommendations={recommendations}
                        columns={getEditProductColumnsWithAdd(handleProductAction, collectionProducts)}
                    />
                </EuiFlexItem>
            </EuiFlexGroup>

            <EuiSpacer size="l" />

            <EuiButton iconType="arrowRight" iconSide="right" onClick={onNext}>
                Continue to Performance
            </EuiButton>
        </>
    );
};

export default ThemeProductsTab;
