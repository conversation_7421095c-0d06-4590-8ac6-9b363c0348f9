import {
    EuiBasicTable,
    EuiFieldSearch,
    EuiFlexGroup,
    EuiFlexItem,
    EuiSelect,
    EuiSpacer,
    EuiTitle,
} from '@elastic/eui';
import React from 'react';

interface Product {
    id: string;
    image: string;
    name: string;
    conversion: number;
    margin: number;
    dhf: number;
    volume: number;
    isPinned?: boolean;
}

interface ProductSearchProps {
    searchProducts: Product[];
    searchPagination: any;
    getSearchColumns: () => any[];
    handleSearchPageChange: (params: any) => void;
}

const ProductSearch: React.FC<ProductSearchProps> = ({
    searchProducts,
    searchPagination,
    getSearchColumns,
    handleSearchPageChange,
}) => {
    return (
        <>
            <EuiTitle size="s">
                <h3>Find products</h3>
            </EuiTitle>
            <EuiSpacer size="m" />

            {/* Search Controls */}
            <EuiFlexGroup gutterSize="m">
                <EuiFlexItem>
                    <EuiSelect
                        options={[
                            { value: 'all', text: 'Categories' },
                            { value: 'category1', text: 'Category 1' },
                            { value: 'category2', text: 'Category 2' },
                        ]}
                    />
                </EuiFlexItem>
                <EuiFlexItem grow={2}>
                    <EuiFieldSearch placeholder="Search" />
                </EuiFlexItem>
            </EuiFlexGroup>

            <EuiSpacer size="m" />

            {/* Search Results Table with Pagination */}
            <EuiBasicTable
                items={searchProducts}
                columns={getSearchColumns()}
                pagination={searchPagination}
                onChange={handleSearchPageChange}
            />
        </>
    );
};

export default ProductSearch;
