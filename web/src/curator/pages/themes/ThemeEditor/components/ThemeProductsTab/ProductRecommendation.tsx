import {
    EuiBasicTable,
    EuiBasicTableColumn,
    EuiSpacer,
    EuiText,
    EuiTitle,
} from '@elastic/eui';
import React from 'react';
import { EditProduct } from '@/curator/pages/themes/shared/productColumns';

interface ProductRecommendationProps {
    recommendations: EditProduct[];
    columns: EuiBasicTableColumn<EditProduct>[];
}

const ProductRecommendation: React.FC<ProductRecommendationProps> = ({
    recommendations,
    columns,
}) => {
    return (
        <>
            <EuiTitle size="s">
                <h3>Recommended</h3>
            </EuiTitle>
            <EuiText size="s" color="subdued">
                Based on added products
            </EuiText>
            <EuiSpacer size="m" />

           
            <EuiBasicTable
                items={recommendations}
                columns={columns}
            />
        </>
    );
};

export default ProductRecommendation;
