import {
    EuiFlexGroup,
    EuiFlexItem,
    EuiPanel,
    EuiSpacer,
    EuiText,
    EuiTitle,
    EuiIcon,
    EuiDragDropContext,
    EuiDroppable,
    EuiDraggable,
} from '@elastic/eui';
import React from 'react';
import { ActionCell, ImageCell, MetricCell } from '@/curator/components/table/cells';
import { EditProduct } from '@/curator/pages/themes/shared/productColumns';

interface ProductCollectionProps {
    collectionProducts: EditProduct[];
    onDragEnd: (result: any) => void;
    handleProductAction: (action: string, productId: string) => void;
}

const ProductCollection: React.FC<ProductCollectionProps> = ({
    collectionProducts,
    onDragEnd,
    handleProductAction,
}) => {
    const collectionActions = [
        {
            id: 'pin',
            label: 'Pin',
            iconType: 'pin',
            onClick: (item: EditProduct) => handleProductAction('pin', item.id),
            isToggled: (item: EditProduct) => item.pinned || false,
            toggledIcon: 'pinFilled',
        },
        {
            id: 'remove',
            label: 'Remove',
            iconType: 'cross',
            onClick: (item: EditProduct) => handleProductAction('remove', item.id),
        },
    ];
    return (
        <>
            <EuiTitle size="s">
                <h3>Collection</h3>
            </EuiTitle>
            <EuiSpacer size="m" />

            {/* Collection Table Header */}
            <div style={{
                backgroundColor: '#F5F7FA',
                padding: '12px 16px',
                fontWeight: 600,
                fontSize: '14px',
                color: '#343741',
                marginBottom: '8px',
                border: '1px solid #D3DAE6',
                borderRadius: '6px 6px 0 0'
            }}>
                <EuiFlexGroup gutterSize="s" alignItems="center">
                    <EuiFlexItem grow={false} style={{ width: 40 }} />
                    <EuiFlexItem>Product ID</EuiFlexItem>
                    <EuiFlexItem grow={false} style={{ width: 48 }}></EuiFlexItem>
                    <EuiFlexItem>Product name</EuiFlexItem>
                    <EuiFlexItem>Conversion</EuiFlexItem>
                    <EuiFlexItem>Margin</EuiFlexItem>
                    <EuiFlexItem>DHF</EuiFlexItem>
                    <EuiFlexItem>Volume</EuiFlexItem>
                    <EuiFlexItem grow={false} style={{ width: 100 }}>Actions</EuiFlexItem>
                </EuiFlexGroup>
            </div>

            {/* Collection Table Body - Drag & Drop */}
            <div style={{
                borderTop: 'none',
                borderRadius: '0 0 6px 6px',
                overflow: 'hidden'
            }}>
                <EuiDragDropContext onDragEnd={onDragEnd}>
                    <EuiDroppable
                        droppableId="COLLECTION_PRODUCTS"
                        spacing="none"
                        withPanel={false}
                    >
                        {collectionProducts.map((product, idx) => (
                            <EuiDraggable
                                spacing="none"
                                key={product.id}
                                index={idx}
                                draggableId={product.id}
                                customDragHandle={true}
                                hasInteractiveChildren={true}
                            >
                                {(provided, snapshot) => (
                                    <EuiPanel
                                        paddingSize="s"
                                        hasShadow={false}
                                        style={{
                                            margin: '0 0 1px 0',
                                            borderRadius: 0,
                                            borderBottom: '1px solid #d3dae6',
                                            boxShadow: 'none'
                                        }}
                                    >
                                        <EuiFlexGroup alignItems="center" gutterSize="s">
                                            <EuiFlexItem grow={false} style={{ width: 40 }}>
                                                <div
                                                    {...provided.dragHandleProps}
                                                    style={{
                                                        cursor: 'grab',
                                                        padding: '4px',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                    }}
                                                >
                                                    <EuiIcon type="grab" />
                                                </div>
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <EuiText size="s">{product.id}</EuiText>
                                            </EuiFlexItem>

                                            <EuiFlexItem grow={false} style={{ width: 48 }}>
                                                <ImageCell src={product.image} size="s" data={{}} alt="" />
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <EuiText size="s">{product.name}</EuiText>
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <MetricCell data={{}} value={product.conversion} suffix=" %" />
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <MetricCell data={{}} value={product.margin} suffix="%" />
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <MetricCell data={{}} value={product.dhf} />
                                            </EuiFlexItem>

                                            <EuiFlexItem>
                                                <MetricCell data={{}} value={product.volume} />
                                            </EuiFlexItem>

                                            <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                <ActionCell
                                                    item={product}
                                                    actions={collectionActions}
                                                />
                                            </EuiFlexItem>
                                        </EuiFlexGroup>
                                    </EuiPanel>
                                )}
                            </EuiDraggable>
                        ))}
                    </EuiDroppable>
                </EuiDragDropContext>
            </div>
        </>
    );
};

export default ProductCollection;
