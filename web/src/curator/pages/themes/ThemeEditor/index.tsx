import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import type { ActionBarItem } from '@/curator/components';
import { transformBreadcrumbsWithIcons, useCuratorBreadcrumbs } from '@/curator/components';
import { defaultThemeTabs, type ThemeTabId } from '@/curator/components/general';

import DetailsTab from '../shared/DetailsTab';
import PerformanceTab from '../shared/PerformanceTab';
import ThemePageLayout from '../shared/ThemePageLayout';
import { useThemesData } from '../ThemesList/hooks/useThemesData';
import ThemeProductsTab from './ProductsTab';

const ThemeEditor: React.FC = () => {
    const { themeId } = useParams<{ themeId: string }>();
    const navigate = useNavigate();
    const [selectedTabId, setSelectedTabId] = useState<ThemeTabId>('details');

    const { themes, isLoading } = useThemesData();
    const themeData = themeId ? themes.find((t) => t.id.toString() === themeId) : null;

    const curatorBreadcrumbs = useCuratorBreadcrumbs(
        themeId && themeData
            ? { text: themeData.name, iconType: 'pencil', path: `/curator/themes/${themeId}/edit` }
            : { text: 'New Theme', iconType: 'pencil', path: '/curator/themes/new' },
    );

    const breadcrumbs = transformBreadcrumbsWithIcons([
        ...curatorBreadcrumbs,
        {
            text: defaultThemeTabs.find((tab) => tab.id === selectedTabId)?.name || 'Tab',
        },
    ]);

    const items: ActionBarItem[] = [
        {
            id: 'status',
            type: 'badge',
            label: themeData?.status || 'Incomplete',
            color: 'warning',
        },
        {
            id: 'save',
            type: 'action',
            label: themeId ? 'Update' : 'Save',
            iconType: 'save',
            onClick: () => navigate('/curator/themes'),
        },
    ];

    const handleTabClick = (tabId: string) => {
        setSelectedTabId(tabId as ThemeTabId);
    };

    const renderTabContent = () => {
        switch (selectedTabId) {
            case 'performance':
                return (
                    <PerformanceTab
                        performance={null}
                        placements={themeData?.placement ? [themeData.placement] : []}
                        isLoading={false}
                    />
                );
            case 'details':
                return <DetailsTab themeData={themeData} onNext={() => setSelectedTabId('products')} />;
            case 'products':
                return <ThemeProductsTab onNext={() => setSelectedTabId('performance')} />;
            default:
                return null;
        }
    };

    if (themeId && isLoading) {
        return (
            <ThemePageLayout
                breadcrumbs={breadcrumbs}
                actionItems={items}
                title="Loading theme..."
                selectedTabId={selectedTabId}
                onTabClick={handleTabClick}
            >
                <div>Loading theme...</div>
            </ThemePageLayout>
        );
    }

    if (themeId && !isLoading && !themeData) {
        return (
            <ThemePageLayout
                breadcrumbs={breadcrumbs}
                actionItems={items}
                title="Theme not found"
                selectedTabId={selectedTabId}
                onTabClick={handleTabClick}
            >
                <div>Theme not found</div>
            </ThemePageLayout>
        );
    }

    return (
        <ThemePageLayout
            breadcrumbs={breadcrumbs}
            actionItems={items}
            title={themeId ? `${themeData?.name || 'Theme'}` : 'Create New Theme'}
            selectedTabId={selectedTabId}
            onTabClick={handleTabClick}
        >
            {renderTabContent()}
        </ThemePageLayout>
    );
};

export default ThemeEditor;
