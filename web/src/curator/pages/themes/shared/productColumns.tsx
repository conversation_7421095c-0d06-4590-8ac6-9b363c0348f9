import { EuiBasicTableColumn } from '@elastic/eui';
import type { TableAction } from '@/curator/components/table/cells';
import { ActionCell, ImageCell, MetricCell, TextValueCell } from '@/curator/components/table/cells';

// Base Product interface - common fields across view and edit
export interface BaseProduct {
    id: string;
    image: string;
    name: string;
    margin: number;
    volume: number;
    isPinned?: boolean;
}

// View mode specific fields
export interface ViewProduct extends BaseProduct {
    ctr: number;
    impressions: number;
    gmiClick: string;
    isPinned: boolean; // Required in view mode
}

// Edit mode specific fields
export interface EditProduct extends BaseProduct {
    conversion: number;
    dhf: number;
}

export type ProductActionHandler = (action: string, productId: string) => void;

/**
 * Base product columns that are common across ALL product tables
 * (ID, Image, Name - these appear in both view and edit modes)
 */
const getBaseProductColumns = <T extends BaseProduct>(): EuiBasicTableColumn<T>[] => [
    {
        field: 'id',
        name: 'Product ID',
        width: '100px',
        render: (id: string, product: T) => (
            <TextValueCell data={product} value={id} />
        ),
    },
    {
        field: 'image',
        name: '',
        width: '60px',
        render: (image: string, product: T) => (
            <ImageCell src={image} size="s" data={product} alt="" />
        ),
    },
    {
        field: 'name',
        name: 'Product name',
        render: (name: string, product: T) => (
            <TextValueCell data={product} value={name} />
        ),
    },
];

/**
 * Common metric columns that appear in both view and edit
 * (Margin, Volume)
 */
const getCommonMetricColumns = <T extends BaseProduct>(): EuiBasicTableColumn<T>[] => [
    {
        field: 'margin',
        name: 'Margin',
        render: (margin: number, product: T) => (
            <MetricCell data={product} value={margin} suffix="%" />
        ),
    },
    {
        field: 'volume',
        name: 'Volume',
        render: (volume: number, product: T) => (
            <MetricCell data={product} value={volume} />
        ),
    },
];

/**
 * VIEW MODE SPECIFIC COLUMNS
 * These columns only appear in theme view (ctr, impressions, gmiClick)
 */
const getViewSpecificColumns = (): EuiBasicTableColumn<ViewProduct>[] => [
    {
        field: 'ctr',
        name: 'CTR ↓',
        render: (ctr: number, product: ViewProduct) => (
            <MetricCell data={product} value={ctr} suffix=" %" />
        ),
    },
    {
        field: 'impressions',
        name: 'Impressions',
        render: (impressions: number, product: ViewProduct) => (
            <MetricCell data={product} value={impressions} />
        ),
    },
    {
        field: 'gmiClick',
        name: 'GMI/click',
        render: (gmiClick: string, product: ViewProduct) => (
            <MetricCell data={product} value={gmiClick} />
        ),
    },
];

/**
 * EDIT MODE SPECIFIC COLUMNS  
 * These columns only appear in theme editor (conversion, dhf)
 */
const getEditSpecificColumns = (): EuiBasicTableColumn<EditProduct>[] => [
    {
        field: 'conversion',
        name: 'Conversion',
        render: (conversion: number, product: EditProduct) => (
            <MetricCell data={product} value={conversion} suffix=" %" />
        ),
    },
    {
        field: 'dhf',
        name: 'DHF',
        render: (dhf: number, product: EditProduct) => (
            <MetricCell data={product} value={dhf} />
        ),
    },
];

/**
 * PINNED STATUS COLUMNS
 */
const getPinnedStatusColumn = <T extends BaseProduct>(): EuiBasicTableColumn<T> => ({
    field: 'isPinned',
    name: 'Pinned',
    width: '80px',
    render: (isPinned: boolean, product: T) => (
        <TextValueCell 
            data={product} 
            value={isPinned ? '📌' : ''} 
        />
    ),
});

/**
 * ACTION COLUMNS
 */
const getActionsColumn = <T extends BaseProduct>(
    actions: TableAction<T>[]
): EuiBasicTableColumn<T> => ({
    field: 'actions',
    name: 'Actions',
    width: '80px',
    render: (_: any, product: T) => (
        <ActionCell item={product} actions={actions} />
    ),
});

// =============================================================================
// PUBLIC API - EXPORTED FUNCTIONS
// =============================================================================

/**
 * VIEW MODE: Complete columns for theme view (read-only with pinned status)
 */
export const getViewProductColumns = (): EuiBasicTableColumn<ViewProduct>[] => [
    ...getBaseProductColumns<ViewProduct>(),
    ...getViewSpecificColumns(),
    ...getCommonMetricColumns<ViewProduct>(),
    getPinnedStatusColumn<ViewProduct>(),
];

/**
 * EDIT MODE: Columns for search/recommendation tables (with add action)
 */
export const getEditProductColumnsWithAdd = (
    handleProductAction: ProductActionHandler,
    collectionProducts: EditProduct[]
): EuiBasicTableColumn<EditProduct>[] => {
    const addActions: TableAction<EditProduct>[] = [
        {
            id: 'add',
            label: 'Add',
            iconType: 'plus',
            onClick: (item: EditProduct) => handleProductAction('add', item.id),
            isCompleted: (item: EditProduct) => collectionProducts.some(p => p.id === item.id),
            completedIcon: 'check',
        },
    ];

    return [
        ...getBaseProductColumns<EditProduct>(),
        ...getEditSpecificColumns(),
        ...getCommonMetricColumns<EditProduct>(),
        getActionsColumn<EditProduct>(addActions),
    ];
};

/**
 * EDIT MODE: Actions for collection table (pin/remove actions)
 */
export const getEditProductCollectionActions = (
    handleProductAction: ProductActionHandler
): TableAction<EditProduct>[] => [
    {
        id: 'pin',
        label: 'Pin',
        iconType: 'pin',
        onClick: (item: EditProduct) => handleProductAction('pin', item.id),
        isToggled: (item: EditProduct) => item.isPinned || false,
        toggledIcon: 'pinFilled',
    },
    {
        id: 'remove',
        label: 'Remove',
        iconType: 'cross',
        onClick: (item: EditProduct) => handleProductAction('remove', item.id),
    },
];

/**
 * EDIT MODE: Read-only columns for edit mode (no actions)
 */
export const getEditProductColumnsReadOnly = (): EuiBasicTableColumn<EditProduct>[] => [
    ...getBaseProductColumns<EditProduct>(),
    ...getEditSpecificColumns(),
    ...getCommonMetricColumns<EditProduct>(),
];
